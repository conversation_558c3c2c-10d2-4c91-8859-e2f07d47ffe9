#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    分布式数据库系统                          ║"
    echo "║                基于2PC（二阶段提交）协议                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示菜单
show_menu() {
    echo -e "${CYAN}请选择要执行的操作:${NC}"
    echo
    echo -e "${GREEN}1.${NC} 🎬 观看系统演示"
    echo -e "${GREEN}2.${NC} 🚀 完整系统设置"
    echo -e "${GREEN}3.${NC} 🐳 启动数据库容器"
    echo -e "${GREEN}4.${NC} 🗄️  初始化数据库"
    echo -e "${GREEN}5.${NC} 🧪 运行系统测试"
    echo -e "${GREEN}6.${NC} 🌐 启动Web界面"
    echo -e "${GREEN}7.${NC} 📊 查看系统状态"
    echo -e "${GREEN}8.${NC} 🔄 执行完整流程"
    echo -e "${RED}9.${NC} ❌ 退出"
    echo
}

# 等待用户按键
wait_for_key() {
    echo -e "${YELLOW}按任意键继续...${NC}"
    read -n 1 -s
}

# 执行命令并处理错误
execute_command() {
    local cmd="$1"
    local desc="$2"
    
    echo -e "${BLUE}$desc${NC}"
    if eval "$cmd"; then
        echo -e "${GREEN}✅ 操作成功完成${NC}"
    else
        echo -e "${RED}❌ 操作失败${NC}"
    fi
    wait_for_key
}

# 主循环
main() {
    while true; do
        clear
        show_banner
        show_menu
        
        echo -n "请输入选项 (1-9): "
        read choice
        
        case $choice in
            1)
                execute_command "python3 demo.py" "🎬 启动系统演示..."
                ;;
            2)
                execute_command "python3 main.py setup" "🚀 执行完整系统设置..."
                ;;
            3)
                execute_command "python3 main.py start-db" "🐳 启动数据库容器..."
                ;;
            4)
                execute_command "python3 main.py init-db" "🗄️ 初始化数据库..."
                ;;
            5)
                execute_command "python3 main.py test" "🧪 运行系统测试..."
                ;;
            6)
                echo -e "${BLUE}🌐 启动Web界面...${NC}"
                echo -e "${YELLOW}浏览器将自动打开 http://localhost:5000${NC}"
                
                # 尝试打开浏览器
                if command -v xdg-open > /dev/null; then
                    xdg-open http://localhost:5000 &
                elif command -v open > /dev/null; then
                    open http://localhost:5000 &
                fi
                
                python3 main.py web
                wait_for_key
                ;;
            7)
                execute_command "python3 main.py status" "📊 查看系统状态..."
                ;;
            8)
                execute_command "python3 main.py all" "🔄 执行完整流程..."
                ;;
            9)
                echo -e "${GREEN}👋 感谢使用分布式数据库系统！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选项，请重新选择${NC}"
                wait_for_key
                ;;
        esac
    done
}

# 检查Python是否安装
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 未安装，请先安装Python3${NC}"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ 未找到 requirements.txt 文件${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}检查Python依赖...${NC}"
    if ! python3 -c "import mysql.connector, flask" &> /dev/null; then
        echo -e "${YELLOW}⚠️ 部分依赖未安装，正在安装...${NC}"
        pip3 install -r requirements.txt
    fi
}

# 启动脚本
echo -e "${BLUE}正在启动分布式数据库系统...${NC}"
check_python
check_dependencies
main
