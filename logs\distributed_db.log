2025-05-27 16:34:58,356 - database - ERROR - Database connection Connection pool creation failed for node db1 FAILED - localhost:3306: 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
2025-05-27 16:34:58,356 - database - ERROR - Failed to create connection pool for db1: 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
2025-05-27 16:35:02,453 - database - ERROR - Database connection Connection pool creation failed for node db2 FAILED - localhost:3307: 2003 (HY000): Can't connect to MySQL server on 'localhost:3307' (10061)
2025-05-27 16:35:02,454 - database - ERROR - Failed to create connection pool for db2: 2003 (HY000): Can't connect to MySQL server on 'localhost:3307' (10061)
2025-05-27 16:35:02,456 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,718 - transaction - INFO - Transaction d312b7ce-f27b-4b4b-b715-63f888c3a109 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,724 - transaction - INFO - Transaction 720843eb-7bda-46de-944c-4dca40bcba8d started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,727 - transaction - INFO - Transaction 720843eb-7bda-46de-944c-4dca40bcba8d started successfully
2025-05-27 16:35:02,728 - transaction - INFO - Transaction 1aafeb6a-a2fa-40dd-9d8d-c268cfa1cf22 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,729 - system - ERROR - System error in TransactionManager.begin_transaction: Connection failed
2025-05-27 16:35:02,731 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,733 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 prepare phase - Participant participant_1: SUCCESS
2025-05-27 16:35:02,734 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 prepare phase - Participant participant_2: SUCCESS
2025-05-27 16:35:02,734 - transaction - INFO - Transaction 35af7ec7-e3c2-495e-a2a4-63f85411a6f0 prepared successfully
2025-05-27 16:35:02,736 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,737 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de prepare phase - Participant participant_1: FAILED
2025-05-27 16:35:02,738 - system - ERROR - System error in TransactionManager.prepare: Prepare failed for participant_1: Prepare failed
2025-05-27 16:35:02,749 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de ROLLBACK
2025-05-27 16:35:02,751 - transaction - INFO - Transaction 894f5635-7717-434d-9f1b-f8638b2609de rolled back successfully
2025-05-27 16:35:02,753 - transaction - INFO - Transaction 055206f6-4c0e-48e3-8047-b8129864c40b started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,754 - transaction - INFO - Transaction 055206f6-4c0e-48e3-8047-b8129864c40b COMMITTED
2025-05-27 16:35:02,754 - transaction - INFO - Transaction 055206f6-4c0e-48e3-8047-b8129864c40b committed successfully
2025-05-27 16:35:02,755 - transaction - INFO - Transaction 858aeb41-2ad3-4598-8265-b77963b91f6e started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,757 - transaction - INFO - Transaction 858aeb41-2ad3-4598-8265-b77963b91f6e ROLLBACK
2025-05-27 16:35:02,757 - transaction - INFO - Transaction 858aeb41-2ad3-4598-8265-b77963b91f6e rolled back successfully
2025-05-27 16:35:02,759 - transaction - INFO - Transaction e8a3f593-c865-4d71-8ce0-47434a2e8ecd started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,761 - transaction - INFO - Transaction 97a537b8-1242-467e-8900-25a4741e5bb2 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,763 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 16:35:02,764 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 16:35:02,764 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,766 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 16:35:02,767 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 16:35:02,768 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,769 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 16:35:02,771 - database - INFO - Database connection Connection pool created for node db1 - localhost:3306
2025-05-27 16:35:02,772 - database - INFO - Database connection Connection pool created for node db2 - localhost:3307
2025-05-27 16:35:02,773 - database - INFO - Database manager initialized with nodes: db1, db2
2025-05-27 16:35:02,774 - database - INFO - Database connection Connection pool created for node test - localhost:3306
2025-05-27 16:35:02,777 - system - INFO - BankingService: Account 1001 created with balance 1000.0
2025-05-27 16:35:02,780 - system - ERROR - System error in BankingService.create_account: Database error
2025-05-27 16:35:02,788 - transaction - INFO - Transaction a6022967-0169-490e-bf63-c9ff42f84e93 started with participants: ['participant_1', 'participant_2', 'participant_3', 'participant_4', 'participant_5', 'participant_6', 'participant_7', 'participant_8', 'participant_9', 'participant_10']
2025-05-27 16:35:02,789 - transaction - INFO - Transaction a6022967-0169-490e-bf63-c9ff42f84e93 started successfully
2025-05-27 16:35:02,791 - transaction - INFO - Transaction 8cedb74b-0bca-49aa-92fb-b84606675f0a started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,792 - transaction - INFO - Transaction e5b373b1-1fd7-455a-9181-f08b6f12f4e8 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,792 - transaction - INFO - Transaction 619a97b7-a2f0-441b-8aff-6fdd2b1ee87f started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,792 - transaction - INFO - Transaction 7999f19c-db63-4a98-8672-8a90f9a65996 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,793 - transaction - INFO - Transaction 82582a8e-46ab-49df-a058-7bcc6fb9ec12 started with participants: ['participant_1', 'participant_2']
2025-05-27 16:35:02,794 - transaction - INFO - Transaction 8cedb74b-0bca-49aa-92fb-b84606675f0a started successfully
2025-05-27 16:35:02,794 - transaction - INFO - Transaction e5b373b1-1fd7-455a-9181-f08b6f12f4e8 started successfully
2025-05-27 16:35:02,795 - transaction - INFO - Transaction 619a97b7-a2f0-441b-8aff-6fdd2b1ee87f started successfully
2025-05-27 16:35:02,796 - transaction - INFO - Transaction 7999f19c-db63-4a98-8672-8a90f9a65996 started successfully
2025-05-27 16:35:02,796 - transaction - INFO - Transaction 82582a8e-46ab-49df-a058-7bcc6fb9ec12 started successfully
