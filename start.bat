@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    分布式数据库系统                          ║
echo ║                基于2PC（二阶段提交）协议                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:menu
echo 请选择要执行的操作:
echo.
echo 1. 🎬 观看系统演示
echo 2. 🚀 完整系统设置
echo 3. 🐳 启动数据库容器
echo 4. 🗄️  初始化数据库
echo 5. 🧪 运行系统测试
echo 6. 🌐 启动Web界面
echo 7. 📊 查看系统状态
echo 8. 🔄 执行完整流程
echo 9. ❌ 退出
echo.

set /p choice="请输入选项 (1-9): "

if "%choice%"=="1" goto demo
if "%choice%"=="2" goto setup
if "%choice%"=="3" goto start_db
if "%choice%"=="4" goto init_db
if "%choice%"=="5" goto test
if "%choice%"=="6" goto web
if "%choice%"=="7" goto status
if "%choice%"=="8" goto all
if "%choice%"=="9" goto exit
goto invalid

:demo
echo.
echo 🎬 启动系统演示...
python demo.py
pause
goto menu

:setup
echo.
echo 🚀 执行完整系统设置...
python main.py setup
pause
goto menu

:start_db
echo.
echo 🐳 启动数据库容器...
python main.py start-db
pause
goto menu

:init_db
echo.
echo 🗄️ 初始化数据库...
python main.py init-db
pause
goto menu

:test
echo.
echo 🧪 运行系统测试...
python main.py test
pause
goto menu

:web
echo.
echo 🌐 启动Web界面...
echo 浏览器将自动打开 http://localhost:5000
start http://localhost:5000
python main.py web
pause
goto menu

:status
echo.
echo 📊 查看系统状态...
python main.py status
pause
goto menu

:all
echo.
echo 🔄 执行完整流程...
python main.py all
pause
goto menu

:invalid
echo.
echo ❌ 无效选项，请重新选择
pause
goto menu

:exit
echo.
echo 👋 感谢使用分布式数据库系统！
pause
exit
