version: '3.8'

services:
  mysql1:
    image: mysql:8.4
    container_name: mysql1
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: db1
      MYSQL_USER: dbuser
      MYSQL_PASSWORD: dbpass
    ports:
      - "3306:3306"
    volumes:
      - mysql1_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    networks:
      - distributed_db_network

  mysql2:
    image: mysql:8.4
    container_name: mysql2
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: db2
      MYSQL_USER: dbuser
      MYSQL_PASSWORD: dbpass
    ports:
      - "3307:3306"
    volumes:
      - mysql2_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    networks:
      - distributed_db_network

  # 可选：添加phpMyAdmin用于数据库管理
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    restart: unless-stopped
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql1,mysql2
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8080:80"
    depends_on:
      - mysql1
      - mysql2
    networks:
      - distributed_db_network

volumes:
  mysql1_data:
    driver: local
  mysql2_data:
    driver: local

networks:
  distributed_db_network:
    driver: bridge
