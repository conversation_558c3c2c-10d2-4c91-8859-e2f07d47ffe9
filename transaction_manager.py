import mysql.connector
from mysql.connector import Error

class TransactionManager:
    def __init__(self, connections):
        self.connections = connections
        
    def begin_transaction(self):
        """Start a distributed transaction"""
        for conn in self.connections:
            cursor = conn.cursor()
            cursor.execute("XA START 'trx1'")
            
    def prepare(self):
        """Phase 1: Prepare"""
        for conn in self.connections:
            cursor = conn.cursor()
            cursor.execute("XA END 'trx1'")
            cursor.execute("XA PREPARE 'trx1'")
            
    def commit(self):
        """Phase 2: Commit"""
        for conn in self.connections:
            cursor = conn.cursor()
            cursor.execute("XA COMMIT 'trx1'")
            
    def rollback(self):
        """Phase 2: Rollback"""
        for conn in self.connections:
            cursor = conn.cursor()
            cursor.execute("XA ROLLBACK 'trx1'")