import mysql.connector

def setup_databases():
    # Setup first database
    conn1 = mysql.connector.connect(
        host="localhost", port=3306,
        user="root", password="password"
    )
    cursor1 = conn1.cursor()
    cursor1.execute("CREATE DATABASE IF NOT EXISTS db1")
    cursor1.execute("USE db1")
    cursor1.execute("""
    CREATE TABLE IF NOT EXISTS accounts (
        id INT PRIMARY KEY,
        balance DECIMAL(10, 2)
    )
    """)
    conn1.commit()
    
    # Setup second database
    conn2 = mysql.connector.connect(
        host="localhost", port=3307,
        user="root", password="password"
    )
    cursor2 = conn2.cursor()
    cursor2.execute("CREATE DATABASE IF NOT EXISTS db2")
    cursor2.execute("USE db2")
    cursor2.execute("""
    CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_id INT,
        amount DECIMAL(10, 2),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    conn2.commit()
    
    conn1.close()
    conn2.close()

if __name__ == "__main__":
    setup_databases()