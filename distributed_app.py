import mysql.connector
from transaction_manager import TransactionManager

def connect_to_databases():
    connections = []
    try:
        # Connect to first MySQL instance
        conn1 = mysql.connector.connect(
            host="localhost", port=3306,
            user="root", password="password",
            database="db1"
        )
        connections.append(conn1)
        
        # Connect to second MySQL instance
        conn2 = mysql.connector.connect(
            host="localhost", port=3307,
            user="root", password="password",
            database="db2"
        )
        connections.append(conn2)
        
        return connections
    except Error as e:
        print(f"Error connecting to MySQL: {e}")
        return None

def execute_distributed_transaction():
    connections = connect_to_databases()
    if not connections:
        return False
    
    tm = TransactionManager(connections)
    
    try:
        # Begin transaction
        tm.begin_transaction()
        
        # Execute operations on both databases
        cursor1 = connections[0].cursor()
        cursor1.execute("INSERT INTO accounts (id, balance) VALUES (1, 1000)")
        
        cursor2 = connections[1].cursor()
        cursor2.execute("INSERT INTO transactions (account_id, amount) VALUES (1, 1000)")
        
        # Prepare phase
        tm.prepare()
        
        # Commit phase
        tm.commit()
        print("Transaction committed successfully")
        return True
    except Exception as e:
        print(f"Transaction failed: {e}")
        tm.rollback()
        return False
    finally:
        for conn in connections:
            conn.close()

if __name__ == "__main__":
    execute_distributed_transaction()